// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  })),
  QueryCommand: jest.fn(),
  ScanCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  unmarshall: jest.fn((item) => {
    // Simple mock implementation that converts DynamoDB format to plain object
    const result = {}
    for (const [key, value] of Object.entries(item)) {
      if (value.S) result[key] = value.S
      else if (value.N) result[key] = parseInt(value.N)
      else if (value.L) result[key] = value.L.map(v => v.S || v.N || v)
      else result[key] = value
    }
    return result
  })
}))

const { handler } = require('../handlers/listAllTracks')

describe('listAllTracks Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.TRACKS_TABLE_NAME = 'TunamiTracks-test'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (queryParams = {}, userId = null) => ({
    queryStringParameters: queryParams,
    requestContext: userId ? {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    } : {}
  })

  const mockTrackItem = (overrides = {}) => ({
    trackId: { S: 'track-123' },
    title: { S: 'Test Track' },
    genre: { S: 'Electronic' },
    description: { S: 'A test track' },
    aiToolsUsed: { L: [{ S: 'AIVA' }, { S: 'Amper' }] },
    audioFileUrl: { S: 'https://example.com/track.mp3' },
    coverImageUrl: { S: 'https://example.com/cover.jpg' },
    uploadDate: { S: '2024-01-15T10:00:00Z' },
    isPublic: { S: 'true' },
    tags: { L: [{ S: 'electronic' }, { S: 'ai' }] },
    fileSize: { N: '5000000' },
    duration: { N: '180' },
    contentType: { S: 'audio/mpeg' },
    listenCount: { N: '42' },
    likeCount: { N: '15' },
    commentCount: { N: '5' },
    creatorId: { S: 'creator-123' },
    createdAt: { S: '2024-01-15T10:00:00Z' },
    updatedAt: { S: '2024-01-15T10:00:00Z' },
    searchableText: { S: 'test track electronic a test track electronic ai aiva amper' },
    ...overrides
  })

  describe('Success Cases', () => {
    test('should list public tracks with default parameters', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.tracks).toHaveLength(1)
      expect(response.data.tracks[0]).toMatchObject({
        trackId: 'track-123',
        title: 'Test Track',
        genre: 'Electronic',
        isPublic: true,
        creator: expect.objectContaining({
          userId: 'creator-123'
        })
      })
      expect(response.data.pagination.hasMore).toBe(false)
    })

    test('should filter tracks by genre', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ genre: 'Electronic' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.filters.genre).toBe('Electronic')

      // Verify filter expression was used
      expect(mockDynamoSend).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: expect.stringContaining('#genre = :genre')
        })
      )
    })

    test('should filter tracks by tags', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ tags: 'electronic,ai' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.filters.tags).toEqual(['electronic', 'ai'])

      // Verify filter expression was used
      expect(mockDynamoSend).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: expect.stringContaining('contains(#tags')
        })
      )
    })

    test('should search tracks by text', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ search: 'test track' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.filters.search).toBe('test track')

      // Verify filter expression was used
      expect(mockDynamoSend).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: expect.stringContaining('contains(#searchableText')
        })
      )
    })

    test('should sort tracks by popularity', async () => {
      const track1 = mockTrackItem({ 
        trackId: { S: 'track-1' },
        title: { S: 'Track 1' },
        likeCount: { N: '10' },
        listenCount: { N: '100' }
      })
      const track2 = mockTrackItem({ 
        trackId: { S: 'track-2' },
        title: { S: 'Track 2' },
        likeCount: { N: '20' },
        listenCount: { N: '200' }
      })
      
      const mockResponse = {
        Items: [track1, track2],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ sortBy: 'popular' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.tracks[0].trackId).toBe('track-2') // Higher popularity first
      expect(response.data.tracks[1].trackId).toBe('track-1')
    })

    test('should handle pagination', async () => {
      const lastEvaluatedKey = { trackId: { S: 'track-123' } }
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: lastEvaluatedKey
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ limit: '10' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.pagination.hasMore).toBe(true)
      expect(response.data.pagination.nextToken).toBeDefined()
      expect(response.data.pagination.limit).toBe(10)
    })

    test('should list tracks by specific creator', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ creatorId: 'creator-123' }, 'creator-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.tracks[0].isOwner).toBe(true)

      // Verify DynamoDB was called (the IndexName is set on the QueryCommand, not the send call)
      expect(mockDynamoSend).toHaveBeenCalledTimes(1)
    })
  })

  describe('Validation Errors', () => {
    test('should reject invalid limit', async () => {
      const event = createMockEvent({ limit: '150' }) // Over max limit

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid sortBy', async () => {
      const event = createMockEvent({ sortBy: 'invalid' })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Error Handling', () => {
    test('should handle DynamoDB errors', async () => {
      const dbError = new Error('DynamoDB error')
      dbError.name = 'ResourceNotFoundException'
      mockDynamoSend.mockRejectedValueOnce(dbError)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TABLE_NOT_FOUND')
    })

    test('should handle invalid pagination token', async () => {
      const event = createMockEvent({ lastEvaluatedKey: 'invalid-token' })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_PAGINATION')
    })

    test('should handle throttling errors', async () => {
      const throttleError = new Error('Throttling')
      throttleError.name = 'ThrottlingException'
      mockDynamoSend.mockRejectedValueOnce(throttleError)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(503)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('SERVICE_THROTTLED')
    })
  })
})
