// Mock services before importing handler
const mockCognitoService = {
  authenticateUser: jest.fn()
}
const mockDynamoService = {
  getUserByEmail: jest.fn()
}

jest.mock('../services/cognitoService', () => {
  return jest.fn().mockImplementation(() => mockCognitoService)
})
jest.mock('../services/dynamoService', () => {
  return jest.fn().mockImplementation(() => mockDynamoService)
})

const { handler } = require('../handlers/loginUser')

describe('loginUser Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const createMockEvent = (body, httpMethod = 'POST') => ({
    httpMethod,
    body: JSON.stringify(body)
  })

  describe('Success Cases', () => {
    test('should login user successfully', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockAuthResult = {
        accessToken: 'mock-access-token',
        idToken: 'mock-id-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      }

      const mockUserData = {
        userId: 'test-user-123',
        username: 'testuser',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00.000Z',
        isActive: true
      }

      mockCognitoService.authenticateUser.mockResolvedValue(mockAuthResult)
      mockDynamoService.getUserByEmail.mockResolvedValue(mockUserData)

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      // Debug output
      if (result.statusCode !== 200) {
        console.log('Unexpected result:', JSON.stringify(result, null, 2))
      }

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.user).toMatchObject({
        userId: mockUserData.userId,
        username: mockUserData.username,
        email: mockUserData.email,
        createdAt: mockUserData.createdAt
      })
      expect(response.data.tokens).toMatchObject({
        accessToken: mockAuthResult.accessToken,
        idToken: mockAuthResult.idToken,
        refreshToken: mockAuthResult.refreshToken,
        expiresIn: mockAuthResult.expiresIn
      })

      expect(mockCognitoService.authenticateUser).toHaveBeenCalledWith('<EMAIL>', 'TestPass123')
      expect(mockDynamoService.getUserByEmail).toHaveBeenCalledWith('<EMAIL>')
    })

    test('should handle CORS preflight requests', async () => {
      const event = createMockEvent({}, 'OPTIONS')
      const result = await handler(event)

      expect(result.statusCode).toBe(200)
      expect(result.headers).toHaveProperty('Access-Control-Allow-Origin', '*')
      expect(result.headers).toHaveProperty('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS')
      expect(result.body).toBe('')
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing email', async () => {
      const requestBody = {
        password: 'TestPass123'
      }

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject missing password', async () => {
      const requestBody = {
        email: '<EMAIL>'
      }

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid email format', async () => {
      const requestBody = {
        email: 'invalid-email',
        password: 'TestPass123'
      }

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid JSON', async () => {
      const event = {
        httpMethod: 'POST',
        body: 'invalid-json'
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_JSON')
    })
  })

  describe('Authentication Errors', () => {
    test('should handle invalid credentials', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'WrongPassword'
      }

      mockCognitoService.authenticateUser.mockRejectedValue(new Error('Invalid email or password'))

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_CREDENTIALS')
    })

    test('should handle user not found in Cognito', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      mockCognitoService.authenticateUser.mockRejectedValue(new Error('User not found'))

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_CREDENTIALS')
    })

    test('should handle Cognito authentication errors', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      mockCognitoService.authenticateUser.mockRejectedValue(new Error('Cognito service error'))

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('AUTH_ERROR')
    })
  })

  describe('Database Errors', () => {
    test('should handle user not found in DynamoDB', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockAuthResult = {
        accessToken: 'mock-access-token',
        idToken: 'mock-id-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      }

      mockCognitoService.authenticateUser.mockResolvedValue(mockAuthResult)
      mockDynamoService.getUserByEmail.mockResolvedValue(null)

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('DATA_INCONSISTENCY')
    })

    test('should handle deactivated user account', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockAuthResult = {
        accessToken: 'mock-access-token',
        idToken: 'mock-id-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      }

      const mockUserData = {
        userId: 'test-user-123',
        username: 'testuser',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00.000Z',
        isActive: false // Deactivated account
      }

      mockCognitoService.authenticateUser.mockResolvedValue(mockAuthResult)
      mockDynamoService.getUserByEmail.mockResolvedValue(mockUserData)

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(403)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('ACCOUNT_DEACTIVATED')
    })

    test('should handle DynamoDB errors', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockAuthResult = {
        accessToken: 'mock-access-token',
        idToken: 'mock-id-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      }

      mockCognitoService.authenticateUser.mockResolvedValue(mockAuthResult)
      mockDynamoService.getUserByEmail.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent(requestBody)
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('DATABASE_ERROR')
    })
  })
})
