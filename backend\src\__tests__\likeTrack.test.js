// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  })),
  PutItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  marshall: jest.fn((obj) => obj),
  unmarshall: jest.fn((obj) => obj)
}))

const { handler } = require('../handlers/likeTrack')

describe('likeTrack Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.LIKES_TABLE_NAME = 'test-likes-table'
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (trackId = 'test-track-123', userId = 'test-user-123') => ({
    pathParameters: {
      trackId: trackId
    },
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should like a track successfully', async () => {
      // Mock: track exists, like doesn't exist
      mockDynamoSend
        .mockResolvedValueOnce({ Item: { trackId: { S: 'test-track-123' } } }) // Check track exists (checkTrackExists)
        .mockResolvedValueOnce({ Item: undefined }) // Check existing like (getCurrentLike)
        .mockResolvedValueOnce({}) // Create like (addLike)
        .mockResolvedValueOnce({}) // Update track like count (updateTrackLikeCount)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.action).toBe('like')

      // Verify all DynamoDB operations were called
      expect(mockDynamoSend).toHaveBeenCalledTimes(4)
    })

    test('should unlike a track successfully', async () => {
      // Mock: track exists, like exists
      mockDynamoSend
        .mockResolvedValueOnce({ Item: { trackId: { S: 'test-track-123' } } }) // Check track exists (checkTrackExists)
        .mockResolvedValueOnce({ Item: { userId: { S: 'test-user-123' }, trackId: { S: 'test-track-123' } } }) // Check existing like (getCurrentLike)
        .mockResolvedValueOnce({}) // Delete like (removeLike)
        .mockResolvedValueOnce({}) // Update track like count (updateTrackLikeCount)

      const event = {
        pathParameters: {
          trackId: 'test-track-123'
        },
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        },
        body: JSON.stringify({ action: 'unlike' })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.action).toBe('unlike')

      // Verify all DynamoDB operations were called
      expect(mockDynamoSend).toHaveBeenCalledTimes(4)
    })

    test('should handle track not found', async () => {
      // Mock: track doesn't exist (checkTrackExists returns false)
      mockDynamoSend
        .mockResolvedValueOnce({ Item: undefined }) // Check track exists (checkTrackExists)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })

    test('should handle already liked track', async () => {
      // Mock: track exists, like already exists
      mockDynamoSend
        .mockResolvedValueOnce({ Item: { trackId: { S: 'test-track-123' } } }) // Check track exists (checkTrackExists)
        .mockResolvedValueOnce({ Item: { userId: { S: 'test-user-123' }, trackId: { S: 'test-track-123' } } }) // Check existing like (getCurrentLike)

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(409)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('ALREADY_LIKED')
    })

    test('should handle unlike when not liked', async () => {
      // Mock: track exists, like doesn't exist
      mockDynamoSend
        .mockResolvedValueOnce({ Item: { trackId: { S: 'test-track-123' } } }) // Check track exists (checkTrackExists)
        .mockResolvedValueOnce({ Item: undefined }) // Check existing like (getCurrentLike)

      const event = {
        pathParameters: {
          trackId: 'test-track-123'
        },
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        },
        body: JSON.stringify({ action: 'unlike' })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(409)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('NOT_LIKED')
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing trackId', async () => {
      const event = {
        pathParameters: {},
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        }
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_TRACK_ID')
    })

    test('should reject invalid action', async () => {
      const event = {
        pathParameters: {
          trackId: 'test-track-123'
        },
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        },
        body: JSON.stringify({ action: 'invalid' })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_ACTION')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const event = {
        pathParameters: {
          trackId: 'test-track-123'
        },
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      // Mock: track exists, like check succeeds, but addLike fails
      mockDynamoSend
        .mockResolvedValueOnce({ Item: { trackId: { S: 'test-track-123' } } }) // Check track exists (checkTrackExists)
        .mockResolvedValueOnce({ Item: undefined }) // Check existing like (getCurrentLike)
        .mockRejectedValueOnce(new Error('DynamoDB error')) // Create like (addLike) fails

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('LIKE_ERROR')
    })
  })
})
