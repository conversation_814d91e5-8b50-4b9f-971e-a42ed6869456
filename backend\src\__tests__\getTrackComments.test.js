// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  })),
  QueryCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  unmarshall: jest.fn((item) => {
    // Simple mock implementation that converts DynamoDB format to plain object
    const result = {}
    for (const [key, value] of Object.entries(item)) {
      if (value.S) result[key] = value.S
      else if (value.N) result[key] = parseInt(value.N)
      else if (value.L) result[key] = value.L.map(v => v.S || v.N || v)
      else result[key] = value
    }
    return result
  })
}))

const { handler } = require('../handlers/getTrackComments')

describe('getTrackComments Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.COMMENTS_TABLE_NAME = 'test-comments-table'
    process.env.USERS_TABLE_NAME = 'test-users-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (trackId, queryParams = {}) => ({
    pathParameters: {
      trackId: trackId
    },
    queryStringParameters: queryParams
  })

  const mockCommentItem = (overrides = {}) => ({
    commentId: { S: 'comment-123' },
    trackId: { S: 'track-123' },
    userId: { S: 'user-123' },
    content: { S: 'Great track!' },
    createdAt: { S: '2024-01-15T10:00:00Z' },
    updatedAt: { S: '2024-01-15T10:00:00Z' },
    ...overrides
  })

  const mockUserItem = (overrides = {}) => ({
    userId: { S: 'user-123' },
    username: { S: 'testuser' },
    profileImageUrl: { S: 'https://example.com/profile.jpg' },
    ...overrides
  })

  describe('Success Cases', () => {
    test('should get track comments successfully', async () => {
      const mockComment = mockCommentItem()
      const mockUser = mockUserItem()

      mockDynamoSend
        .mockResolvedValueOnce({ 
          Items: [mockComment],
          LastEvaluatedKey: null
        }) // Get comments
        .mockResolvedValueOnce({ Items: [mockUser] }) // Get user info

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toMatchObject({
        trackId: 'track-123',
        comments: [{
          commentId: 'comment-123',
          content: 'Great track!',
          createdAt: '2024-01-15T10:00:00Z',
          user: {
            userId: 'user-123',
            username: 'testuser',
            profileImageUrl: 'https://example.com/profile.jpg'
          }
        }],
        pagination: {
          limit: 20,
          nextToken: null,
          hasMore: false,
          sortOrder: 'desc'
        },
        totalCount: 1
      })

      expect(mockDynamoSend).toHaveBeenCalledTimes(2)
    })

    test('should handle custom query parameters', async () => {
      const mockComment = mockCommentItem()
      const mockUser = mockUserItem()

      mockDynamoSend
        .mockResolvedValueOnce({ 
          Items: [mockComment],
          LastEvaluatedKey: { commentId: { S: 'comment-123' } }
        }) // Get comments
        .mockResolvedValueOnce({ Items: [mockUser] }) // Get user info

      const event = createMockEvent('track-123', {
        limit: '10',
        sortOrder: 'asc'
      })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.pagination.limit).toBe(10)
      expect(response.data.pagination.sortOrder).toBe('asc')
      expect(response.data.pagination.hasMore).toBe(true)
      expect(response.data.pagination.nextToken).toBeDefined()
    })

    test('should handle comments with missing user info', async () => {
      const mockComment = mockCommentItem()

      mockDynamoSend
        .mockResolvedValueOnce({ 
          Items: [mockComment],
          LastEvaluatedKey: null
        }) // Get comments
        .mockResolvedValueOnce({ Items: [] }) // No user found

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.comments[0].user).toMatchObject({
        userId: 'user-123',
        username: 'Unknown User',
        profileImageUrl: null
      })
    })

    test('should handle user query failure gracefully', async () => {
      const mockComment = mockCommentItem()

      mockDynamoSend
        .mockResolvedValueOnce({ 
          Items: [mockComment],
          LastEvaluatedKey: null
        }) // Get comments
        .mockRejectedValueOnce(new Error('User query failed')) // User query fails

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.comments[0].user).toMatchObject({
        userId: 'user-123',
        username: 'Unknown User',
        profileImageUrl: null
      })
    })

    test('should handle empty comments list', async () => {
      mockDynamoSend.mockResolvedValueOnce({ 
        Items: [],
        LastEvaluatedKey: null
      })

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.comments).toHaveLength(0)
      expect(response.data.totalCount).toBe(0)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing trackId', async () => {
      const event = {
        pathParameters: {},
        queryStringParameters: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_TRACK_ID')
    })

    test('should reject invalid limit', async () => {
      const event = createMockEvent('track-123', {
        limit: '150' // Over max limit
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid sortOrder', async () => {
      const event = createMockEvent('track-123', {
        sortOrder: 'invalid'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should handle invalid pagination token', async () => {
      const event = createMockEvent('track-123', {
        lastEvaluatedKey: 'invalid-token'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_PAGINATION')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      mockDynamoSend.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('RETRIEVAL_ERROR')
    })
  })
})
