// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  })),
  GetItemCommand: jest.fn(),
  QueryCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  unmarshall: jest.fn((item) => {
    // Simple mock implementation that converts DynamoDB format to plain object
    const result = {}
    for (const [key, value] of Object.entries(item)) {
      if (value.S) result[key] = value.S
      else if (value.N) result[key] = parseInt(value.N)
      else if (value.L) result[key] = value.L.map(v => v.S || v.N || v)
      else result[key] = value
    }
    return result
  })
}))

const { handler } = require('../handlers/getTrackDetails')

describe('getTrackDetails Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.USERS_TABLE_NAME = 'test-users-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (trackId, userId = null) => ({
    pathParameters: {
      trackId: trackId
    },
    requestContext: userId ? {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    } : {}
  })

  const mockTrackItem = (overrides = {}) => ({
    trackId: { S: 'track-123' },
    title: { S: 'Test Track' },
    genre: { S: 'Electronic' },
    description: { S: 'A test track' },
    aiToolsUsed: { L: [{ S: 'AIVA' }, { S: 'Amper' }] },
    audioFileUrl: { S: 'https://example.com/track.mp3' },
    coverImageUrl: { S: 'https://example.com/cover.jpg' },
    uploadDate: { S: '2024-01-15T10:00:00Z' },
    isPublic: { S: 'true' },
    tags: { L: [{ S: 'electronic' }, { S: 'ai' }] },
    listenCount: { N: '42' },
    likeCount: { N: '15' },
    commentCount: { N: '5' },
    creatorId: { S: 'creator-123' },
    ...overrides
  })

  const mockCreatorItem = (overrides = {}) => ({
    userId: { S: 'creator-123' },
    username: { S: 'testcreator' },
    profileImageUrl: { S: 'https://example.com/profile.jpg' },
    creatorStatus: { S: 'verified' },
    ...overrides
  })

  describe('Success Cases', () => {
    test('should get public track details successfully', async () => {
      const mockTrack = mockTrackItem()
      const mockCreator = mockCreatorItem()

      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockTrack }) // Get track
        .mockResolvedValueOnce({ Items: [mockCreator] }) // Get creator

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toMatchObject({
        trackId: 'track-123',
        title: 'Test Track',
        genre: 'Electronic',
        description: 'A test track',
        isPublic: true,
        listenCount: 42,
        likeCount: 15,
        commentCount: 5,
        creator: {
          userId: 'creator-123',
          username: 'testcreator',
          profileImageUrl: 'https://example.com/profile.jpg',
          creatorStatus: 'verified'
        },
        isOwner: false
      })

      expect(mockDynamoSend).toHaveBeenCalledTimes(2)
    })

    test('should get track details for owner', async () => {
      const mockTrack = mockTrackItem({ isPublic: { S: 'false' } })
      const mockCreator = mockCreatorItem()

      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockTrack }) // Get track
        .mockResolvedValueOnce({ Items: [mockCreator] }) // Get creator

      const event = createMockEvent('track-123', 'creator-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.isOwner).toBe(true)
      expect(response.data.isPublic).toBe(false)
    })

    test('should handle track without creator info', async () => {
      const mockTrack = mockTrackItem()

      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockTrack }) // Get track
        .mockResolvedValueOnce({ Items: [] }) // No creator found

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.creator).toBe(null)
    })

    test('should handle creator query failure gracefully', async () => {
      const mockTrack = mockTrackItem()

      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockTrack }) // Get track
        .mockRejectedValueOnce(new Error('Creator query failed')) // Creator query fails

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.creator).toBe(null)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing trackId', async () => {
      const event = {
        pathParameters: {},
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_TRACK_ID')
    })
  })

  describe('Not Found Errors', () => {
    test('should handle track not found', async () => {
      mockDynamoSend.mockResolvedValueOnce({ Item: undefined })

      const event = createMockEvent('nonexistent-track')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })

    test('should handle private track access by non-owner', async () => {
      const mockTrack = mockTrackItem({ isPublic: { S: 'false' } })

      mockDynamoSend.mockResolvedValueOnce({ Item: mockTrack })

      const event = createMockEvent('track-123', 'different-user')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })

    test('should handle private track access by unauthenticated user', async () => {
      const mockTrack = mockTrackItem({ isPublic: { S: 'false' } })

      mockDynamoSend.mockResolvedValueOnce({ Item: mockTrack })

      const event = createMockEvent('track-123') // No user ID
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      mockDynamoSend.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent('track-123')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('RETRIEVAL_ERROR')
    })
  })
})
